"""
基于Playwright的Yopmail邮件获取模块
替代原yogo_python_client.py中的邮件获取功能，集成Capsolver解决人机验证
"""

import asyncio
import logging
import re
import yaml
from typing import Optional, List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, BrowserContext
from dataclasses import dataclass


@dataclass
class MailItem:
    """邮件项目数据类"""
    mail_id: str
    sender_name: str
    sender_email: str
    subject: str
    timestamp: str = ""


class CaptchaError(Exception):
    """验证码错误异常"""
    pass


class CapsolverIntegration:
    """Capsolver验证码解决集成 - 使用官方capsolver库"""

    def __init__(self, api_key: str, enable_debug: bool = False):
        self.api_key = api_key
        self.enable_debug = enable_debug
        self.logger = logging.getLogger(__name__)

        # 设置capsolver API密钥
        try:
            import capsolver
            capsolver.api_key = api_key
            self.capsolver = capsolver
            self.logger.info("✅ Capsolver官方库已初始化")
        except ImportError:
            self.logger.error("❌ 未安装capsolver库，请运行: pip install capsolver")
            self.capsolver = None

    async def solve_recaptcha_v2(self, site_key: str, page_url: str) -> Optional[str]:
        """
        解决reCAPTCHA v2验证码 - 使用官方capsolver库

        Args:
            site_key: reCAPTCHA站点密钥
            page_url: 页面URL

        Returns:
            验证码解决token，失败返回None
        """
        if not self.capsolver:
            self.logger.error("❌ Capsolver库未初始化")
            return None

        if not self.api_key:
            self.logger.warning("❌ Capsolver API密钥未配置，跳过自动解决验证码")
            return None

        try:
            self.logger.info("🤖 开始使用Capsolver解决验证码...")

            # 使用官方库的正确参数格式
            task_params = {
                "type": "ReCaptchaV2TaskProxyLess",
                "websiteKey": site_key,
                "websiteURL": page_url
            }

            # 调用官方库的solve方法
            solution = self.capsolver.solve(task_params)

            if solution and 'gRecaptchaResponse' in solution:
                token = solution['gRecaptchaResponse']
                self.logger.info(f"✅ Capsolver成功解决验证码！")
                return token
            else:
                self.logger.error(f"❌ 解决验证码失败")
                return None

        except Exception as e:
            self.logger.error(f"❌ Capsolver解决验证码异常: {e}")
            import traceback
            self.logger.error(f"❌ 异常详情: {traceback.format_exc()}")
            return None


class PlaywrightYopManager:
    """基于Playwright的Yopmail管理器"""
    
    # Yopmail相关常量
    YOPMAIL_BASE_URL = "https://yopmail.com"
    YOPMAIL_SITE_KEY = "6LcG5v8SAAAAAOdAn2iqMEQTdVyX8t0w9T3cpdN2"
    
    # 页面选择器
    SELECTORS = {
        "inbox_iframe": "#ifinbox",
        "mail_content_iframe": "#ifmail", 
        "mail_item": "div.m",
        "mail_count": "div:has-text('mail')",
        "recaptcha_anchor": 'iframe[src*="recaptcha/api2/anchor"]',
        "recaptcha_response": 'textarea[name="g-recaptcha-response"]',
        "captcha_container": 'div:has-text("Complete the CAPTCHA")'
    }
    
    # Resend邮件识别条件
    RESEND_EMAIL_FILTERS = {
        "sender_email": "<EMAIL>",
        "sender_name": "Resend",
        "subject_keywords": ["Confirm", "Resend", "account"],
        "link_domain": "resend.com",
        "link_path_pattern": r"/auth/confirm-account\?token=",
        "button_text": "Confirm Account"
    }
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, enable_debug: bool = False):
        self.config = config or {}
        self.enable_debug = enable_debug
        self.logger = logging.getLogger(__name__)
        
        # 初始化Capsolver（如果配置了）
        capsolver_config = self.config.get('capsolver', {})
        api_key = capsolver_config.get('api_key', '').strip()

        if api_key:
            self.capsolver = CapsolverIntegration(
                api_key=api_key,
                enable_debug=enable_debug
            )
            self.logger.info(f"✅ Capsolver已初始化")
        else:
            self.capsolver = None
            self.logger.warning("⚠️ Capsolver API密钥未配置，将使用手动解决验证码")
            
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None

        # 状态管理 - 用于智能导航和页面复用
        self.current_username = None  # 当前打开的账户
        self.current_page = None      # 当前收件箱页面
        self.last_access_time = None  # 上次访问时间

        # YopMail配置
        self.YOPMAIL_BASE_URL = "https://yopmail.com"
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 不关闭浏览器，保持复用
        pass
    
    async def start_browser(self):
        """启动浏览器（本地直连模式）"""
        try:
            playwright = await async_playwright().start()

            # 从配置中获取无头模式设置，默认为False（显示浏览器）
            playwright_config = self.config.get('playwright_yop', {})
            headless_mode = playwright_config.get('browser_headless', False)

            self.logger.info(f"🌐 启动浏览器 - 无头模式: {headless_mode}")

            # 使用本地浏览器直连模式（不使用代理）
            self.browser = await playwright.chromium.launch(
                headless=headless_mode,  # 从配置文件读取
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1280, 'height': 720}
            )
            
            if self.enable_debug:
                self.logger.debug("浏览器启动成功（本地直连模式）")
                
        except Exception as e:
            self.logger.error(f"启动浏览器失败: {e}")
            raise
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
                
            if self.enable_debug:
                self.logger.debug("浏览器已关闭")
                
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {e}")
    
    async def smart_navigate_to_inbox(self, username: str, is_retry: bool = False) -> Page:
        """
        智能导航到指定用户的收件箱
        - 新账户：在现有标签页导航到yopmail主页，重新输入账户
        - 重试：刷新当前收件箱页面

        Args:
            username: 用户名
            is_retry: 是否为重试操作

        Returns:
            收件箱页面对象
        """
        try:
            import time
            current_time = time.time()

            # 判断是否为同账户重试
            if (is_retry and
                self.current_username == username and
                self.current_page and
                not self.current_page.is_closed()):

                self.logger.info(f"🔄 刷新当前收件箱: {username}@yopmail.com")
                await self.current_page.reload(wait_until='domcontentloaded')
                await self.current_page.wait_for_timeout(2000)

            else:
                # 新账户或首次访问
                self.logger.info(f"🔄 导航到新收件箱: {username}@yopmail.com")

                # 复用现有页面或创建新页面
                if self.current_page and not self.current_page.is_closed():
                    page = self.current_page
                    await page.goto(self.YOPMAIL_BASE_URL, wait_until='domcontentloaded')
                else:
                    if not self.context:
                        raise Exception("浏览器上下文未初始化，请先调用 start_browser()")
                    page = await self.context.new_page()
                    await page.goto(self.YOPMAIL_BASE_URL, wait_until='domcontentloaded')
                    self.current_page = page

                # 等待页面完全加载
                await page.wait_for_timeout(2000)

                # 输入用户名并进入收件箱
                await page.fill('input[name="login"]', username)

                # 点击进入收件箱按钮
                try:
                    await page.locator('input[name="login"]').press('Enter')
                except:
                    await page.locator('input[name="login"] + * button').click()

                # 等待收件箱页面加载
                await page.wait_for_load_state('domcontentloaded')
                await page.wait_for_timeout(3000)

                # 验证是否成功进入收件箱
                if '/wm' not in page.url:
                    raise Exception("未能成功进入收件箱页面")

                # 更新状态
                self.current_username = username

            self.last_access_time = current_time
            self.logger.info(f"✅ 成功进入收件箱: {username}@yopmail.com")
            return self.current_page

        except Exception as e:
            self.logger.error(f"智能导航到收件箱失败: {e}")
            raise

    async def navigate_to_inbox(self, username: str) -> Page:
        """
        导航到指定用户的收件箱

        Args:
            username: 邮箱用户名（不含@yopmail.com）

        Returns:
            页面对象
        """
        if not self.context:
            raise Exception("浏览器未启动")

        page = await self.context.new_page()

        try:
            # 导航到yopmail主页
            await page.goto(self.YOPMAIL_BASE_URL, wait_until='domcontentloaded')

            # 等待页面完全加载
            await page.wait_for_timeout(2000)

            # 输入用户名
            await page.fill('input[name="login"]', username)

            # 点击进入收件箱按钮
            try:
                # 方法1：按Enter键
                await page.locator('input[name="login"]').press('Enter')
            except:
                # 方法2：点击输入框旁边的按钮
                # 根据页面结构，找到输入框后面的按钮
                await page.locator('input[name="login"] + * button').click()

            # 等待收件箱页面加载
            await page.wait_for_load_state('domcontentloaded')
            await page.wait_for_timeout(3000)

            # 验证是否成功进入收件箱
            if '/wm' not in page.url:
                raise Exception("未能成功进入收件箱页面")

            if self.enable_debug:
                self.logger.debug(f"成功导航到收件箱: {username}@yopmail.com")

            return page

        except Exception as e:
            await page.close()
            raise Exception(f"导航到收件箱失败: {e}")
    
    async def check_and_solve_captcha(self, page: Page) -> bool:
        """
        检查并解决验证码 - 基于真实页面元素分析

        Args:
            page: 页面对象

        Returns:
            是否成功解决验证码
        """
        try:
            self.logger.info("🔍 等待页面加载并检查人机验证...")

            # 等待人机验证加载（最多10秒）
            captcha_detected = False
            for i in range(10):
                await page.wait_for_timeout(1000)  # 等待1秒

                # 检查父级窗口人机验证（检查是否有reCAPTCHA iframe）
                parent_has_captcha = await page.evaluate('''() => {
                    const container = document.querySelector('#r_parent');
                    if (!container || container.style.display === 'none') return false;
                    const recaptchaIframe = container.querySelector('iframe[src*="recaptcha/api2/anchor"]');
                    return recaptchaIframe !== null;
                }''')

                # 检查iframe人机验证
                iframe_has_captcha = await page.evaluate('''() => {
                    const iframes = document.querySelectorAll('iframe');
                    for (let iframe of iframes) {
                        try {
                            const doc = iframe.contentDocument || iframe.contentWindow.document;
                            if (doc && doc.body && doc.body.textContent.includes("Complete the CAPTCHA to continue")) {
                                return true;
                            }
                        } catch (e) {
                            // 跨域iframe无法访问，忽略
                        }
                    }
                    return false;
                }''')

                if parent_has_captcha:
                    self.logger.info(f"✅ 第{i+1}秒检测到父级窗口人机验证")
                    captcha_detected = True
                    break
                elif iframe_has_captcha:
                    self.logger.info(f"✅ 第{i+1}秒检测到iframe人机验证")
                    captcha_detected = True
                    break
                else:
                    self.logger.info(f"⏳ 第{i+1}秒未检测到人机验证，继续等待...")

            if not captcha_detected:
                self.logger.info("✅ 等待10秒后未检测到任何人机验证，继续执行")
                return True

            # 检测到人机验证，使用父级窗口人机验证解决方式处理
            self.logger.info("🤖 检测到人机验证，开始解决...")

            # 3. 等待reCAPTCHA iframe完全加载
            self.logger.info("🤖 检测到reCAPTCHA v2验证码，等待完全加载...")

            try:
                await page.wait_for_selector('iframe[src*="recaptcha/api2/anchor"]', timeout=10000)
                self.logger.info("✅ reCAPTCHA anchor iframe已加载")
            except:
                self.logger.warning("⚠️ reCAPTCHA anchor iframe加载超时")

            # 4. 检查Capsolver配置
            if not self.capsolver:
                self.logger.warning("⚠️ Capsolver未配置，需要手动解决验证码")
                raise CaptchaError("Capsolver未配置，需要手动解决reCAPTCHA v2验证码")

            # 5. 使用Capsolver自动解决
            if self.capsolver:
                self.logger.info("🚀 开始使用Capsolver自动解决验证码...")

                token = await self.capsolver.solve_recaptcha_v2(
                    site_key=self.YOPMAIL_SITE_KEY,
                    page_url=page.url
                )

                if token:
                    self.logger.info(f"🔑 获得验证码token，开始设置...")

                    # 设置验证码token到页面
                    await page.evaluate(f'''() => {{
                        try {{
                            // 方法1: 直接设置g-recaptcha-response
                            const responseElements = document.querySelectorAll('textarea[name="g-recaptcha-response"]');
                            responseElements.forEach(element => {{
                                element.value = "{token}";
                                element.innerHTML = "{token}";
                                element.style.display = "block";
                            }});

                            // 方法2: 通过grecaptcha API设置
                            if (window.grecaptcha && window.grecaptcha.getResponse) {{
                                const callback = window.grecaptcha.getResponse.callback;
                                if (callback && typeof callback === 'function') {{
                                    callback("{token}");
                                }}
                            }}
                        }} catch(e) {{
                            console.error("设置token失败:", e);
                        }}
                    }}''')

                    # 调用yopmail的专用回调函数
                    await page.evaluate(f'''() => {{
                        try {{
                            // 首先确保grecaptcha.getResponse()返回我们的token
                            if (window.grecaptcha && window.grecaptcha.getResponse) {{
                                // 重写getResponse函数返回我们的token
                                const originalGetResponse = window.grecaptcha.getResponse;
                                window.grecaptcha.getResponse = function() {{
                                    return "{token}";
                                }};

                                // 调用yopmail的RcCallback函数
                                if (window.RcCallback && typeof window.RcCallback === 'function') {{
                                    window.RcCallback();
                                    return true;
                                }} else {{
                                    // 如果没有RcCallback，尝试直接调用refr函数
                                    if (window.refr && typeof window.refr === 'function') {{
                                        window.refr(window.page || 'wm', null, null, null, "{token}");

                                        // 手动隐藏验证码
                                        if (window.hideRc && typeof window.hideRc === 'function') {{
                                            window.hideRc();
                                        }}
                                        return true;
                                    }}
                                }}
                            }}

                            return false;
                        }} catch(e) {{
                            return false;
                        }}
                    }}''')

                    # 等待验证码处理
                    await page.wait_for_timeout(5000)

                    # 检查验证码是否已解决
                    captcha_status = await page.evaluate('''() => {
                        const container = document.querySelector('#r_parent');
                        return {
                            containerExists: !!container,
                            containerVisible: container ? container.style.display !== 'none' : false
                        };
                    }''')

                    captcha_gone = not captcha_status['containerExists'] or not captcha_status['containerVisible']

                    if captcha_gone:
                        self.logger.info("✅ 验证码已解决")

                        # 检查页面URL是否发生变化（可能被重定向）
                        current_url = page.url
                        if "yopmail.com" not in current_url or "/wm" not in current_url:
                            # 重新导航到收件箱
                            await page.goto(f"https://yopmail.com/zh/wm")
                            await page.wait_for_load_state('domcontentloaded')

                        return True
                    else:
                        # 尝试查找并点击可能的提交按钮
                        try:
                            submit_clicked = await page.evaluate('''() => {
                                const buttons = document.querySelectorAll('button, input[type="submit"]');
                                for (let btn of buttons) {
                                    if (btn.textContent.includes('提交') || btn.textContent.includes('Submit') ||
                                        btn.type === 'submit') {
                                        btn.click();
                                        return true;
                                    }
                                }
                                return false;
                            }''')

                            if submit_clicked:
                                await page.wait_for_timeout(3000)
                                final_check = await page.evaluate('''() => {
                                    const container = document.querySelector('#r_parent');
                                    return !container || container.style.display === 'none';
                                }''')

                                if final_check:
                                    self.logger.info("✅ 验证码已解决")
                                    current_url = page.url
                                    if "yopmail.com" not in current_url or "/wm" not in current_url:
                                        await page.goto(f"https://yopmail.com/zh/wm")
                                        await page.wait_for_load_state('domcontentloaded')
                                    return True
                        except:
                            pass

                        self.logger.error("❌ 验证码解决失败")
                        return False
                else:
                    self.logger.warning("❌ Capsolver解决验证码失败")
            
            # 如果自动解决失败，抛出异常要求手动处理
            raise CaptchaError("需要手动解决reCAPTCHA v2验证码")
            
        except CaptchaError:
            raise
        except Exception as e:
            self.logger.error(f"验证码处理失败: {e}")
            return False
    
    async def get_resend_verification_link_simple(self, page: Page) -> Optional[str]:
        """
        简化版本：直接从邮件按钮获取验证链接

        Args:
            page: 收件箱页面对象

        Returns:
            验证链接，未找到返回None
        """
        try:
            self.logger.info("🔍 查找Resend验证链接...")

            # 等待页面加载
            await page.wait_for_timeout(3000)

            # 直接在整个页面中查找所有包含resend.com的链接
            verification_links = await page.evaluate('''() => {
                const foundLinks = [];

                // 1. 查找页面中所有包含resend.com的链接
                const allLinks = document.querySelectorAll('a[href*="resend.com"]');
                for (let link of allLinks) {
                    const href = link.href;
                    if (href && href.includes('confirm-account')) {
                        foundLinks.push({
                            href: href,
                            text: link.textContent?.trim() || '',
                            source: 'direct'
                        });
                    }
                }

                // 2. 如果没找到，在iframe中查找
                if (foundLinks.length === 0) {
                    const iframes = document.querySelectorAll('iframe');
                    for (let iframe of iframes) {
                        try {
                            const doc = iframe.contentDocument || iframe.contentWindow.document;
                            const iframeLinks = doc.querySelectorAll('a[href*="resend.com"]');

                            for (let link of iframeLinks) {
                                const href = link.href;
                                if (href && href.includes('confirm-account')) {
                                    foundLinks.push({
                                        href: href,
                                        text: link.textContent?.trim() || '',
                                        source: 'iframe'
                                    });
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }

                // 3. 如果还是没找到，使用正则表达式在整个页面HTML中查找
                if (foundLinks.length === 0) {
                    const htmlContent = document.documentElement.innerHTML;
                    const linkPatterns = [
                        /https:\\/\\/resend\\.com\\/auth\\/confirm-account\\?token=[a-zA-Z0-9]+[^"'\\s<>]*/gi,
                        /https?:\\/\\/[^"'\\s<>]*resend\\.com[^"'\\s<>]*confirm-account[^"'\\s<>]*/gi
                    ];

                    for (let pattern of linkPatterns) {
                        const matches = htmlContent.match(pattern);
                        if (matches && matches.length > 0) {
                            for (let match of matches) {
                                const cleanUrl = match.replace(/&amp;/g, '&').replace(/&gt;.*$/, '').replace(/&lt;.*$/, '');
                                foundLinks.push({
                                    href: cleanUrl,
                                    text: 'Found via regex',
                                    source: 'regex'
                                });
                            }
                            break;
                        }
                    }
                }

                return foundLinks;
            }''')

            # 选择最佳的验证链接
            if verification_links:
                best_link = verification_links[0]['href']
                self.logger.info(f"✅ 找到验证链接")
                return best_link

            # 如果没找到，给用户提示
            self.logger.warning("❌ 未找到Resend验证链接")
            await page.wait_for_timeout(15000)
            return None

        except Exception as e:
            self.logger.error(f"简化获取验证链接失败: {e}")
            return None

    async def get_resend_verification_link_from_page(self, page: Page) -> Optional[str]:
        """
        从收件箱页面直接获取Resend验证链接
        参考原yop模块的逻辑和开发文档的页面结构分析

        Args:
            page: 收件箱页面对象

        Returns:
            验证链接，未找到返回None
        """
        try:
            self.logger.info("🔍 开始在收件箱页面查找Resend邮件...")

            # 等待邮件列表iframe加载
            self.logger.info("⏳ 等待邮件列表iframe加载...")
            await page.wait_for_selector('iframe', timeout=10000)
            await page.wait_for_timeout(2000)  # 额外等待iframe内容加载

            # 1. 获取邮件列表（使用JavaScript访问iframe内容，参考开发文档）
            self.logger.info("� 获取邮件列表...")
            mail_list = await page.evaluate('''() => {
                const iframe = document.querySelector('iframe');
                if (!iframe) return [];

                try {
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    const mailButtons = doc.querySelectorAll('button');

                    return Array.from(mailButtons).map((button, index) => ({
                        index: index,
                        text: button.textContent?.trim() || '',
                        id: button.id || '',
                        className: button.className || ''
                    }));
                } catch (e) {
                    console.log('访问iframe内容失败:', e);
                    return [];
                }
            }''')

            self.logger.info(f"� 找到 {len(mail_list)} 个邮件按钮")

            # 2. 查找Resend邮件（参考原模块的筛选条件）
            resend_mail = None
            for mail in mail_list:
                mail_text = mail['text']


                # 使用原模块的筛选条件
                if ('Resend' in mail_text and 'Confirm' in mail_text) or \
                   ('<EMAIL>' in mail_text) or \
                   ('Confirm your Resend account' in mail_text):
                    resend_mail = mail
                    self.logger.info(f"✅ 找到Resend邮件")
                    break

            if not resend_mail:
                self.logger.warning("❌ 未找到Resend邮件")
                await page.wait_for_timeout(10000)
                return None

            # 点击Resend邮件
            await page.evaluate(f'''() => {{
                const iframe = document.querySelector('iframe');
                if (!iframe) return false;

                try {{
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    const buttons = doc.querySelectorAll('button');
                    const targetButton = buttons[{resend_mail['index']}];

                    if (targetButton) {{
                        targetButton.click();
                        return true;
                    }}
                }} catch (e) {{
                    console.log('点击邮件失败:', e);
                }}
                return false;
            }}''')

            # 4. 等待邮件内容加载
            await page.wait_for_timeout(3000)

            # 5. 从邮件内容iframe提取验证链接（参考原模块的提取逻辑）
            self.logger.info("� 提取验证链接...")
            verification_result = await page.evaluate('''() => {
                const iframes = document.querySelectorAll('iframe');

                for (let iframe of iframes) {
                    try {
                        const doc = iframe.contentDocument || iframe.contentWindow.document;

                        // 查找包含confirm-account的链接
                        const links = doc.querySelectorAll('a[href*="confirm"], a[href*="token"], a[href*="resend.com"]');

                        for (let link of links) {
                            const href = link.href;
                            if (href && href.includes('resend.com') && href.includes('confirm-account')) {
                                return {
                                    href: href,
                                    text: link.textContent?.trim() || ''
                                };
                            }
                        }

                        // 备用方案：使用正则表达式在HTML内容中查找
                        const htmlContent = doc.documentElement.innerHTML;
                        const patterns = [
                            /https:\\/\\/resend\\.com\\/auth\\/confirm-account[^"\\s<>]*/gi,
                            /https?:\\/\\/[^"\\s<>]*resend\\.com[^"\\s<>]*confirm-account[^"\\s<>]*/gi
                        ];

                        for (let pattern of patterns) {
                            const matches = htmlContent.match(pattern);
                            if (matches && matches.length > 0) {
                                return {
                                    href: matches[0].replace(/&amp;/g, '&'),
                                    text: 'Found via regex'
                                };
                            }
                        }

                    } catch (e) {
                        continue;
                    }
                }
                return null;
            }''')

            if verification_result and verification_result['href']:
                verification_link = verification_result['href']
                self.logger.info(f"✅ 成功提取验证链接")
                return verification_link
            else:
                self.logger.warning("❌ 未找到验证链接")
                await page.wait_for_timeout(10000)
                return None

        except Exception as e:
            self.logger.error(f"获取验证链接失败: {e}")
            return None



    async def get_resend_verification_link(self, username: str, max_attempts: int = 5,
                                         wait_interval: int = 5, initial_wait: int = 0) -> Optional[str]:
        """
        获取Resend验证链接的主要方法（支持智能重试）

        Args:
            username: 邮箱用户名（不含@yopmail.com）
            max_attempts: 最大重试次数
            wait_interval: 基础等待间隔（秒）
            initial_wait: 初始等待时间（秒），用于邮件发送后的等待

        Returns:
            验证链接，未找到返回None
        """
        # 初始等待（邮件发送后等待邮件到达）
        if initial_wait > 0:
            self.logger.info(f"⏳ 等待 {initial_wait} 秒让邮件到达...")
            await asyncio.sleep(initial_wait)

        for attempt in range(1, max_attempts + 1):
            try:
                if attempt > 1:
                    self.logger.info(f"🔄 重试 {attempt}/{max_attempts}")

                # 智能导航到收件箱
                is_retry = attempt > 1
                page = await self.smart_navigate_to_inbox(username, is_retry=is_retry)

                # 检查并解决验证码（只在首次访问时检查）
                if not is_retry:
                    captcha_solved = await self.check_and_solve_captcha(page)
                    if not captcha_solved:
                        raise CaptchaError("验证码解决失败")

                # 使用简化方案直接从页面获取验证链接
                verification_link = await self.get_resend_verification_link_simple(page)

                if verification_link:
                    self.logger.info(f"✅ 成功找到验证链接")
                    return verification_link

                # 未找到链接，准备重试
                if attempt < max_attempts:
                    # 递增等待时间：5秒、10秒、15秒...
                    retry_wait = wait_interval * attempt
                    self.logger.info(f"⏳ {retry_wait} 秒后重试...")
                    await asyncio.sleep(retry_wait)

            except CaptchaError:
                self.logger.error("❌ 验证码解决失败")
                raise
            except Exception as e:
                self.logger.error(f"❌ 尝试 {attempt} 失败: {e}")
                if attempt < max_attempts:
                    retry_wait = wait_interval * attempt
                    self.logger.info(f"⏳ {retry_wait} 秒后重试...")
                    await asyncio.sleep(retry_wait)

        self.logger.warning(f"❌ 经过 {max_attempts} 次尝试，未能获取到验证链接")
        return None




def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open('input/config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception:
        return {}


# 全局管理器实例
_global_yop_manager = None

async def _get_global_manager():
    """获取全局YOP管理器实例"""
    global _global_yop_manager

    if _global_yop_manager is None:
        config = load_config()
        _global_yop_manager = PlaywrightYopManager(config=config, enable_debug=True)
        await _global_yop_manager.start_browser()

    return _global_yop_manager

async def shutdown_global_manager():
    """关闭全局管理器"""
    global _global_yop_manager

    if _global_yop_manager:
        await _global_yop_manager.close_browser()
        _global_yop_manager = None

async def get_resend_verification_link(username: str, max_attempts: int = 5, wait_interval: int = 5, initial_wait: int = 5) -> Optional[str]:
    """
    获取指定用户的Resend认证链接（异步版本）

    Args:
        username: 邮箱用户名（不含@yopmail.com）
        max_attempts: 最大尝试次数，默认5次
        wait_interval: 每次等待时间（秒），默认5秒
        initial_wait: 初始等待时间（秒），用于邮件发送后等待，默认5秒

    Returns:
        最新的Resend认证链接，如果没有找到则返回None
    """
    # 获取main程序的logger
    main_logger = logging.getLogger('__main__')
    main_logger.info(f"📧 开始获取验证邮件: {username}@yopmail.com")

    try:
        # 获取全局管理器实例
        yop_manager = await _get_global_manager()

        # 使用新的智能重试机制
        verification_link = await yop_manager.get_resend_verification_link(
            username=username,
            max_attempts=max_attempts,
            wait_interval=wait_interval,
            initial_wait=initial_wait
        )

        if verification_link:
            main_logger.info(f"✅ 成功找到验证链接")
            return verification_link
        else:
            main_logger.error(f"❌ 经过 {max_attempts} 次尝试，仍未获取到验证邮件")
            return None

    except CaptchaError as e:
        main_logger.error(f"❌ 验证码解决失败: {e}")
        return None
    except Exception as e:
        main_logger.error(f"❌ 获取验证邮件失败: {e}")
        return None



# 导出兼容接口
__all__ = ['get_resend_verification_link', 'PlaywrightYopManager', 'CaptchaError', 'shutdown_global_manager']
