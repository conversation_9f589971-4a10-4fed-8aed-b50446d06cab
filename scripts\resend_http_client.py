"""
Resend HTTP客户端模块 - 域名添加和DNS记录获取
使用纯HTTP POST请求替代SDK，解决官方SDK问题

更新说明：
- 完全移除resend SDK依赖
- 使用requests库直接调用Resend API
- 保持原有的智能任务分配、异常处理等完整功能
- 单线程处理，遵守API限制（每秒最多2次请求）
- 智能重试策略和失败处理机制
"""

import os
import sys
import time
import logging
import threading
import yaml
import re
import requests
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum


class FailureType(Enum):
    """失败类型枚举"""
    NETWORK = "network"
    ACCOUNT = "account" 
    DOMAIN = "domain"
    BOTH = "both"
    DNS_FETCH = "dns_fetch"


@dataclass
class AccountInfo:
    """账户信息数据类"""
    api_key: str
    email: str
    password: str
    retry_count: int = 0
    is_failed: bool = False
    failure_reason: Optional[str] = None


@dataclass
class DomainInfo:
    """域名信息数据类"""
    domain: str
    cf_account: str
    is_failed: bool = False
    failure_reason: Optional[str] = None


@dataclass
class TaskResult:
    """任务结果数据类"""
    success: bool
    account: AccountInfo
    domain: DomainInfo
    domain_id: Optional[str] = None
    dkim_key: Optional[str] = None
    failure_type: Optional[FailureType] = None
    error_message: Optional[str] = None


class ResendHTTPClient:
    """Resend HTTP客户端 - 使用纯POST请求"""
    
    def __init__(self):
        self.base_url = "https://api.resend.com"
        self.session = requests.Session()
        
    def _make_request(self, method: str, endpoint: str, api_key: str, data: Optional[Dict] = None) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """发送HTTP请求到Resend API"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "POST":
                response = self.session.post(url, headers=headers, json=data)
            elif method.upper() == "GET":
                response = self.session.get(url, headers=headers)
            else:
                return False, None, f"不支持的HTTP方法: {method}", None
                
            # 检查响应状态
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    return True, result, None, response.status_code
                except json.JSONDecodeError:
                    return False, None, "响应不是有效的JSON格式", response.status_code
            else:
                # 尝试解析错误响应
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', f'HTTP {response.status_code} 错误')
                    return False, None, error_message, response.status_code
                except:
                    return False, None, f"HTTP {response.status_code}: {response.text}", response.status_code
                    
        except requests.exceptions.RequestException as e:
            return False, None, f"网络请求异常: {str(e)}", None
        except Exception as e:
            return False, None, f"未知错误: {str(e)}", None
    
    def create_domain(self, api_key: str, domain_name: str, region: str = "ap-northeast-1") -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """创建域名"""
        data = {
            "name": domain_name,
            "region": region
        }
        return self._make_request("POST", "/domains", api_key, data)
    
    def get_domain(self, api_key: str, domain_id: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """获取域名详情"""
        return self._make_request("GET", f"/domains/{domain_id}", api_key)
    
    def list_domains(self, api_key: str) -> Tuple[bool, Optional[Dict], Optional[str], Optional[int]]:
        """列出所有域名"""
        return self._make_request("GET", "/domains", api_key)


class TaskAllocator:
    """智能任务分配器"""
    
    def __init__(self, accounts: List[AccountInfo], domains: List[DomainInfo]):
        self.accounts = accounts.copy()
        self.domains = domains.copy()
        self.current_account_index = 0
        self.current_domain_index = 0
        self.completed_tasks: List[TaskResult] = []
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def get_next_task(self) -> Optional[Tuple[AccountInfo, DomainInfo]]:
        """获取下一个任务组合（一对一配对模式）"""
        with self.lock:
            # 一对一配对模式：账户文件第N行对应域名文件第N行
            while (self.current_account_index < len(self.accounts) and
                   self.current_domain_index < len(self.domains)):

                account = self.accounts[self.current_account_index]
                domain = self.domains[self.current_domain_index]

                # 任何失败都直接跳过当前账户+域名配对
                if account.is_failed or domain.is_failed:
                    self._advance_indices()  # 直接跳过当前配对，进入下一对
                    continue

                return account, domain

            return None
            
    def handle_task_result(self, result: TaskResult):
        """处理任务结果并决定下一步分配策略"""
        with self.lock:
            self.completed_tasks.append(result)
            
            if result.success:
                self.logger.info(f"✅ 成功: {result.account.email} -> {result.domain.domain}")
                self._advance_indices()
                return
                
            # 根据失败类型处理
            if result.failure_type == FailureType.NETWORK:
                self._handle_network_failure(result)
            elif result.failure_type == FailureType.ACCOUNT:
                self._handle_account_failure(result)
            elif result.failure_type == FailureType.DOMAIN:
                self._handle_domain_failure(result)
            elif result.failure_type == FailureType.BOTH:
                self._handle_both_failure(result)
                
    def _handle_network_failure(self, result: TaskResult):
        """处理网络失败"""
        account = result.account
        account.retry_count += 1

        if account.retry_count >= 3:  # 最大重试3次
            self.logger.warning(f"❌ 网络重试超限，跳过当前配对: {account.email} -> {result.domain.domain}")
            account.is_failed = True
            account.failure_reason = "网络重试超限"
            # 跳过当前配对（一对一模式）
            self._advance_indices()
        else:
            self.logger.info(f"🔄 网络重试 {account.retry_count}/3: {account.email}")
            # 保持当前分配，等待重试

    def _handle_account_failure(self, result: TaskResult):
        """处理账户失败（API密钥无效、账户被封等）"""
        account = result.account
        account.is_failed = True
        account.failure_reason = result.error_message
        self.logger.warning(f"❌ 账户问题，跳过当前配对: {account.email} -> {result.domain.domain}")
        # 跳过当前配对（一对一模式）
        self._advance_indices()

    def _handle_domain_failure(self, result: TaskResult):
        """处理域名失败（域名已被添加等）"""
        domain = result.domain
        domain.is_failed = True
        domain.failure_reason = result.error_message
        self.logger.warning(f"❌ 域名问题，跳过当前配对: {result.account.email} -> {domain.domain}")
        # 跳过当前配对（一对一模式）
        self._advance_indices()
        
    def _handle_both_failure(self, result: TaskResult):
        """处理双重失败"""
        account = result.account
        domain = result.domain
        account.is_failed = True
        domain.is_failed = True
        account.failure_reason = result.error_message
        domain.failure_reason = result.error_message
        self.logger.warning(f"❌ 双重问题，跳过账户和域名: {account.email} -> {domain.domain}")
        self._advance_indices()
        
    def _advance_indices(self):
        """推进索引到下一个组合"""
        self.current_account_index += 1
        self.current_domain_index += 1
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_tasks = len(self.completed_tasks)
        successful_tasks = len([t for t in self.completed_tasks if t.success])
        
        failure_stats = {}
        for failure_type in FailureType:
            count = len([t for t in self.completed_tasks 
                        if not t.success and t.failure_type == failure_type])
            failure_stats[failure_type.value] = count
            
        return {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'failed_tasks': total_tasks - successful_tasks,
            'success_rate': f"{(successful_tasks/total_tasks*100):.1f}%" if total_tasks > 0 else "0%",
            'failure_breakdown': failure_stats,
            'remaining_accounts': len([a for a in self.accounts if not a.is_failed]),
            'remaining_domains': len([d for d in self.domains if not d.is_failed])
        }


class ResendHTTPManager:
    """Resend HTTP主管理器 - 使用纯POST请求替代SDK"""
    
    def __init__(self, config_path: str = "input/config.yaml"):
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        self.http_client = ResendHTTPClient()
        self.task_allocator: Optional[TaskAllocator] = None
        self.request_delay = 0.5  # 每秒最多2次请求，所以间隔0.5秒
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            sys.exit(1)
            
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        # 只使用控制台输出，不创建额外的日志文件
        logger = logging.getLogger(__name__)
        
        # 避免重复配置
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            # 只添加控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger

    def load_accounts_from_file(self, file_path: str = "output/registered_accounts.txt") -> List[AccountInfo]:
        """从文件加载账户信息"""
        accounts = []
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"❌ 账户文件不存在: {file_path}")
                return accounts

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 解析格式: [API密钥]---[邮箱 密码]
                    match = re.match(r'\[([^\]]+)\]---\[([^\s]+)\s+([^\]]+)\]', line)
                    if match:
                        api_key, email, password = match.groups()
                        accounts.append(AccountInfo(
                            api_key=api_key.strip(),
                            email=email.strip(),
                            password=password.strip()
                        ))
                    else:
                        self.logger.warning(f"⚠️ 第{line_num}行格式错误，跳过: {line}")

            self.logger.info(f"✅ 成功加载 {len(accounts)} 个账户")
            return accounts

        except Exception as e:
            self.logger.error(f"❌ 加载账户文件失败: {e}")
            return accounts

    def load_domains_from_file(self, file_path: str = "domains.txt") -> List[DomainInfo]:
        """从文件加载域名信息"""
        domains = []
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"❌ 域名文件不存在: {file_path}")
                return domains

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 解析格式: 域名----CLOUDFLARE账号
                    if '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            domain, cf_account = parts
                            domains.append(DomainInfo(
                                domain=domain.strip(),
                                cf_account=cf_account.strip()
                            ))
                        else:
                            self.logger.warning(f"⚠️ 第{line_num}行格式错误，跳过: {line}")
                    else:
                        self.logger.warning(f"⚠️ 第{line_num}行缺少分隔符'----'，跳过: {line}")

            self.logger.info(f"✅ 成功加载 {len(domains)} 个域名")
            return domains

        except Exception as e:
            self.logger.error(f"❌ 加载域名文件失败: {e}")
            return domains

    def _classify_error(self, error_message: str, status_code: Optional[int] = None) -> FailureType:
        """根据Resend官方错误响应代码分类错误"""
        error_lower = error_message.lower()

        # 优先检查特定的错误消息模式
        # 1. 域名已被注册的情况 - 跳过域名，给账户换下一个域名
        if 'domain has been' in error_lower or 'already exists' in error_lower or 'domain is already' in error_lower:
            return FailureType.DOMAIN
        
        # 2. 账户被封锁的情况 - 跳过账户，给域名换下一个账户
        if ('temporarily suspended' in error_lower or 
            'account suspended' in error_lower or 
            'account is suspended' in error_lower or
            'check your email for more details' in error_lower):
            return FailureType.ACCOUNT

        # 根据HTTP状态码分类（基于官方文档）
        if status_code:
            # 400 - 请求参数问题 (invalid_idempotency_key, validation_error)
            if status_code == 400:
                return FailureType.DOMAIN

            # 401 - 认证问题 (missing_api_key, restricted_api_key)
            elif status_code == 401:
                return FailureType.ACCOUNT

            # 403 - 授权问题 (invalid_api_key, validation_error)
            elif status_code == 403:
                return FailureType.ACCOUNT

            # 404 - 端点不存在 (not_found)
            elif status_code == 404:
                return FailureType.NETWORK

            # 405 - 方法不允许 (method_not_allowed)
            elif status_code == 405:
                return FailureType.NETWORK

            # 409 - 冲突问题 (invalid_idempotent_request, concurrent_idempotent_requests)
            elif status_code == 409:
                return FailureType.DOMAIN

            # 422 - 验证错误 (invalid_attachment, invalid_from_address, invalid_access, invalid_parameter, invalid_region, missing_required_field)
            elif status_code == 422:
                return FailureType.DOMAIN

            # 429 - 限流问题 (daily_quota_exceeded, rate_limit_exceeded)
            elif status_code == 429:
                return FailureType.ACCOUNT

            # 451 - 安全错误 (security_error)
            elif status_code == 451:
                return FailureType.NETWORK

            # 500 - 服务器错误 (application_error, internal_server_error)
            elif status_code == 500:
                return FailureType.NETWORK

        # 根据错误消息内容进行更精确的分类
        # 账户相关错误关键词
        account_keywords = [
            'api key', 'api_key', 'authorization', 'bearer', 'invalid_api_key',
            'missing_api_key', 'restricted_api_key', 'quota', 'rate limit',
            'suspended', 'account', 'temporarily'
        ]
        
        # 域名相关错误关键词
        domain_keywords = [
            'validation', 'invalid', 'missing', 'required', 'parameter',
            'region', 'domain', 'attachment', 'from_address', 'idempotency',
            'already exists', 'has been', 'conflict'
        ]
        
        # 网络相关错误关键词
        network_keywords = [
            'timeout', 'connection', 'network', 'proxy', 'dns',
            'unreachable', 'refused', 'reset', 'ssl', 'certificate',
            'httpclienterror', 'failed to decode', 'unexpected error',
            'connection error', 'socket error', 'read timeout', 'not_found',
            'method_not_allowed', 'security_error', 'application_error',
            'internal_server_error'
        ]

        # 按优先级检查错误类型
        if any(kw in error_lower for kw in account_keywords):
            return FailureType.ACCOUNT
        elif any(kw in error_lower for kw in domain_keywords):
            return FailureType.DOMAIN
        elif any(kw in error_lower for kw in network_keywords):
            return FailureType.NETWORK

        # 默认为网络错误（未知错误类型）
        return FailureType.NETWORK

    def _add_domain_to_resend(self, account: AccountInfo, domain: DomainInfo) -> TaskResult:
        """添加域名到Resend账户 - 使用HTTP POST请求"""
        try:
            self.logger.info(f"🚀 开始添加域名: {account.email} -> {domain.domain}")

            # 调用HTTP客户端添加域名
            success, result, error_message, status_code = self.http_client.create_domain(
                api_key=account.api_key,
                domain_name=domain.domain,
                region="ap-northeast-1"
            )

            if success and result:
                domain_id = result.get('id')
                if not domain_id:
                    raise Exception("未获取到域名ID")

                self.logger.info(f"✅ 域名添加成功: {domain.domain} (ID: {domain_id})")

                return TaskResult(
                    success=True,
                    account=account,
                    domain=domain,
                    domain_id=domain_id
                )
            else:
                failure_type = self._classify_error(error_message or "未知错误", status_code)

                self.logger.error(f"❌ 域名添加失败: {account.email} -> {domain.domain}")
                self.logger.error(f"   状态码: {status_code or 'Unknown'}")
                self.logger.error(f"   错误消息: {error_message}")
                self.logger.error(f"   分类结果: {failure_type.value}")

                return TaskResult(
                    success=False,
                    account=account,
                    domain=domain,
                    failure_type=failure_type,
                    error_message=error_message or "未知错误"
                )

        except Exception as e:
            failure_type = self._classify_error(str(e))

            self.logger.error(f"❌ 域名添加异常: {account.email} -> {domain.domain}")
            self.logger.error(f"   异常信息: {str(e)}")
            self.logger.error(f"   分类结果: {failure_type.value}")

            return TaskResult(
                success=False,
                account=account,
                domain=domain,
                failure_type=failure_type,
                error_message=str(e)
            )

    def _get_domain_dns_records(self, account: AccountInfo, domain_id: str) -> Optional[str]:
        """获取域名的DNS记录，提取DKIM密钥 - 使用HTTP GET请求"""
        try:
            # 调用HTTP客户端获取域名详情
            success, result, error_message, status_code = self.http_client.get_domain(
                api_key=account.api_key,
                domain_id=domain_id
            )

            if not success or not result:
                self.logger.error(f"❌ 获取DNS记录失败: {domain_id}")
                self.logger.error(f"   状态码: {status_code or 'Unknown'}")
                self.logger.error(f"   错误消息: {error_message}")
                return None

            records = result.get('records') if result else None

            if not records:
                self.logger.warning(f"⚠️ 域名 {domain_id} 没有DNS记录")
                return None

            # 查找DKIM记录
            for record in records:
                if not isinstance(record, dict):
                    continue

                record_type = record.get('record', '')
                record_name = record.get('name', '')
                record_value = record.get('value', '')

                # 查找DKIM记录（可能是CNAME类型的domainkey记录）
                if (record_type == 'DKIM' or
                    'domainkey' in record_name or
                    record_type == 'CNAME' and 'domainkey' in record_name):

                    self.logger.info(f"✅ 找到DKIM记录: {record_name} -> {record_value}")
                    return record_value

            self.logger.warning(f"⚠️ 未找到DKIM记录: {domain_id}")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取DNS记录异常: {domain_id}")
            self.logger.error(f"   异常信息: {str(e)}")
            return None

    def _process_single_task(self, task_data: Tuple[AccountInfo, DomainInfo]) -> TaskResult:
        """处理单个任务 - 只添加域名，不获取DKIM"""
        account, domain = task_data

        # 只添加域名，DKIM记录稍后统一获取
        result = self._add_domain_to_resend(account, domain)

        return result

    def run_domain_addition(self) -> Dict[str, Any]:
        """运行域名添加主流程"""
        self.logger.info("🚀 开始Resend域名添加流程 (HTTP POST请求版本)")

        # 加载数据
        accounts = self.load_accounts_from_file()
        domains = self.load_domains_from_file()

        if not accounts:
            self.logger.error("❌ 没有可用的账户")
            return {"success": False, "error": "没有可用的账户"}

        if not domains:
            self.logger.error("❌ 没有可用的域名")
            return {"success": False, "error": "没有可用的域名"}

        self.logger.info(f"📊 加载完成: {len(accounts)} 个账户, {len(domains)} 个域名")

        # 初始化任务分配器
        self.task_allocator = TaskAllocator(accounts, domains)

        # 第一阶段：单线程添加域名（遵守API限制）
        self.logger.info("🚀 第一阶段：开始单线程添加域名...")
        self.logger.info(f"⏱️ 请求间隔: {self.request_delay}秒 (遵守每秒最多2次请求限制)")
        successful_results = []

        task_count = 0
        while True:
            task_data = self.task_allocator.get_next_task()
            if task_data is None:
                break

            task_count += 1
            account, domain = task_data
            self.logger.info(f"📋 处理任务 {task_count}: {account.email} -> {domain.domain}")

            # 处理单个任务
            result = self._process_single_task(task_data)
            self.task_allocator.handle_task_result(result)

            if result.success:
                successful_results.append(result)
                self.logger.info(f"✅ 域名添加成功: {domain.domain}")
            else:
                self.logger.error(f"❌ 域名添加失败: {domain.domain} - {result.error_message}")

            # API限制：每秒最多2次请求，所以等待0.5秒
            if task_count > 1:  # 第一个请求不需要等待
                self.logger.debug(f"⏳ 等待 {self.request_delay} 秒...")
                time.sleep(self.request_delay)

        # 第二阶段：获取DKIM记录
        self.logger.info(f"✅ 第一阶段完成，成功添加 {len(successful_results)} 个域名")

        if successful_results:
            self.logger.info("🔍 第二阶段：开始获取DKIM记录...")

            for i, result in enumerate(successful_results, 1):
                self.logger.info(f"📋 获取DKIM记录 {i}/{len(successful_results)}: {result.domain.domain}")
                dkim_key = self._get_domain_dns_records(result.account, result.domain_id)
                result.dkim_key = dkim_key

                if dkim_key:
                    self.logger.info(f"✅ DKIM获取成功: {result.domain.domain}")
                else:
                    self.logger.warning(f"⚠️ DKIM获取失败: {result.domain.domain}")

                # API限制：每秒最多2次请求
                if i < len(successful_results):  # 最后一个请求不需要等待
                    self.logger.debug(f"⏳ 等待 {self.request_delay} 秒...")
                    time.sleep(self.request_delay)

            self.logger.info("✅ 第二阶段完成，DKIM记录获取完毕")
        else:
            self.logger.warning("⚠️ 没有成功添加的域名，跳过DKIM获取阶段")

        # 获取统计信息
        stats = self.task_allocator.get_statistics()
        self.logger.info("📊 域名添加完成统计:")
        self.logger.info(f"   总任务: {stats['total_tasks']}")
        self.logger.info(f"   成功: {stats['successful_tasks']}")
        self.logger.info(f"   失败: {stats['failed_tasks']}")
        self.logger.info(f"   成功率: {stats['success_rate']}")

        # 保存结果
        self._save_results(successful_results, stats)

        return {
            "success": True,
            "statistics": stats,
            "successful_results": successful_results
        }

    def _save_results(self, successful_results: List[TaskResult], stats: Dict[str, Any]):
        """保存处理结果到单个文件"""
        try:
            # 使用固定文件名，不带时间戳
            results_file = "output/add_domains_list.txt"

            with open(results_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("Resend域名添加和DKIM获取完整结果 (HTTP POST请求版本)\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}\n")
                f.write("=" * 80 + "\n\n")

                # 统计信息
                f.write("📊 统计信息:\n")
                f.write(f"   总任务: {stats['total_tasks']}\n")
                f.write(f"   成功: {stats['successful_tasks']}\n")
                f.write(f"   失败: {stats['failed_tasks']}\n")
                f.write(f"   成功率: {stats['success_rate']}\n")
                f.write("\n" + "-" * 60 + "\n\n")

                # 成功的域名详情
                f.write("✅ 成功添加的域名:\n\n")
                for i, result in enumerate(successful_results, 1):
                    f.write(f"域名 {i}:\n")
                    f.write(f"   域名: {result.domain.domain}\n")
                    f.write(f"   CF账号: {result.domain.cf_account}\n")
                    f.write(f"   Resend账户: {result.account.email}\n")
                    f.write(f"   API密钥: {result.account.api_key}\n")
                    f.write(f"   域名ID: {result.domain_id}\n")
                    f.write(f"   DKIM记录: {result.dkim_key or '未获取到'}\n")
                    f.write(f"   状态: {'✅ 完整成功' if result.dkim_key else '⚠️ 域名添加成功但DKIM获取失败'}\n")
                    f.write("\n")

                # 失败信息
                if self.task_allocator:
                    failed_accounts = [a for a in self.task_allocator.accounts if a.is_failed]
                    failed_domains = [d for d in self.task_allocator.domains if d.is_failed]

                    if failed_accounts:
                        f.write("-" * 60 + "\n")
                        f.write("❌ 失败的账户:\n\n")
                        for i, account in enumerate(failed_accounts, 1):
                            f.write(f"账户 {i}:\n")
                            f.write(f"   邮箱: {account.email}\n")
                            f.write(f"   API密钥: {account.api_key}\n")
                            f.write(f"   失败原因: {account.failure_reason}\n")
                            f.write("\n")

                    if failed_domains:
                        f.write("-" * 60 + "\n")
                        f.write("❌ 失败的域名:\n\n")
                        for i, domain in enumerate(failed_domains, 1):
                            f.write(f"域名 {i}:\n")
                            f.write(f"   域名: {domain.domain}\n")
                            f.write(f"   CF账号: {domain.cf_account}\n")
                            f.write(f"   失败原因: {domain.failure_reason}\n")
                            f.write("\n")

                f.write("=" * 80 + "\n")
                f.write("处理完成\n")

            self.logger.info(f"✅ 完整结果已保存到: {results_file}")
            self.logger.info(f"📊 成功处理 {len(successful_results)} 个域名")

            # 生成错误情况详细报告文件
            self._save_error_report()

        except Exception as e:
            self.logger.error(f"❌ 保存结果失败: {e}")

    def _save_error_report(self):
        """生成错误情况详细报告文件"""
        try:
            if not self.task_allocator:
                return

            # 收集所有失败的任务
            failed_tasks = [task for task in self.task_allocator.completed_tasks if not task.success]
            
            if not failed_tasks:
                self.logger.info("✅ 没有失败的任务，跳过错误报告生成")
                return

            # 生成错误报告文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            error_report_file = f"output/domain_addition_errors_{timestamp}.txt"

            with open(error_report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("Resend域名添加错误情况详细报告\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}\n")
                f.write("=" * 80 + "\n\n")

                # 错误统计
                f.write("📊 错误统计:\n")
                f.write(f"   总失败任务: {len(failed_tasks)} 个\n")
                
                # 按错误类型分类统计
                error_type_stats = {}
                for task in failed_tasks:
                    error_type = task.failure_type.value if task.failure_type else "unknown"
                    error_type_stats[error_type] = error_type_stats.get(error_type, 0) + 1
                
                f.write("   错误类型分布:\n")
                for error_type, count in error_type_stats.items():
                    f.write(f"     {error_type}: {count} 个\n")
                
                f.write("\n" + "-" * 60 + "\n\n")

                # 详细错误信息
                f.write("❌ 详细错误信息:\n\n")
                
                for i, task in enumerate(failed_tasks, 1):
                    f.write(f"错误 {i}:\n")
                    f.write(f"   账户邮箱: {task.account.email}\n")
                    f.write(f"   账户API密钥: {task.account.api_key}\n")
                    f.write(f"   域名: {task.domain.domain}\n")
                    f.write(f"   CF账号: {task.domain.cf_account}\n")
                    f.write(f"   错误类型: {task.failure_type.value if task.failure_type else 'unknown'}\n")
                    f.write(f"   错误消息: {task.error_message}\n")
                    
                    # 根据错误类型说明处理逻辑
                    if task.failure_type == FailureType.DOMAIN:
                        f.write(f"   处理逻辑: 跳过域名 {task.domain.domain}，账户 {task.account.email} 尝试下一个域名\n")
                    elif task.failure_type == FailureType.ACCOUNT:
                        f.write(f"   处理逻辑: 跳过账户 {task.account.email}，域名 {task.domain.domain} 分配给下一个账户\n")
                    elif task.failure_type == FailureType.NETWORK:
                        f.write(f"   处理逻辑: 网络问题，重试或跳过\n")
                    else:
                        f.write(f"   处理逻辑: 未知错误类型\n")
                    
                    f.write("\n")

                # 特定错误类型的汇总
                f.write("-" * 60 + "\n")
                f.write("📋 特定错误类型汇总:\n\n")
                
                # 域名已被注册的情况
                domain_exists_tasks = [task for task in failed_tasks 
                                     if task.failure_type == FailureType.DOMAIN and 
                                     task.error_message and
                                     ('domain has been' in task.error_message.lower() or 
                                      'already exists' in task.error_message.lower())]
                
                if domain_exists_tasks:
                    f.write(f"🔸 域名已被注册 ({len(domain_exists_tasks)} 个):\n")
                    for task in domain_exists_tasks:
                        f.write(f"   - {task.domain.domain} (账户: {task.account.email})\n")
                    f.write("\n")

                # 账户被封锁的情况
                suspended_tasks = [task for task in failed_tasks 
                                 if task.failure_type == FailureType.ACCOUNT and 
                                 task.error_message and
                                 ('suspended' in task.error_message.lower())]
                
                if suspended_tasks:
                    f.write(f"🔸 账户被封锁 ({len(suspended_tasks)} 个):\n")
                    for task in suspended_tasks:
                        f.write(f"   - {task.account.email} (域名: {task.domain.domain})\n")
                    f.write("\n")

                # 其他错误
                other_tasks = [task for task in failed_tasks 
                             if task not in domain_exists_tasks and task not in suspended_tasks]
                
                if other_tasks:
                    f.write(f"🔸 其他错误 ({len(other_tasks)} 个):\n")
                    for task in other_tasks:
                        error_msg = task.error_message or "未知错误"
                        f.write(f"   - {task.account.email} -> {task.domain.domain}: {error_msg[:50]}...\n")
                    f.write("\n")

                f.write("=" * 80 + "\n")
                f.write("错误报告完成\n")

            self.logger.info(f"📄 错误情况详细报告已生成: {error_report_file}")
            self.logger.info(f"   总失败任务: {len(failed_tasks)} 个")
            self.logger.info(f"   域名已被注册: {len(domain_exists_tasks)} 个")
            self.logger.info(f"   账户被封锁: {len(suspended_tasks)} 个")
            self.logger.info(f"   其他错误: {len(other_tasks)} 个")

        except Exception as e:
            self.logger.error(f"❌ 生成错误报告失败: {e}")


def main():
    """主函数"""
    try:
        # 创建管理器
        manager = ResendHTTPManager()

        # 运行域名添加流程
        result = manager.run_domain_addition()

        if result["success"]:
            print("🎉 Resend域名添加流程完成！(HTTP POST请求版本)")
            stats = result["statistics"]
            print(f"📊 统计: 成功 {stats['successful_tasks']} 个, 失败 {stats['failed_tasks']} 个")
            print(f"📈 成功率: {stats['success_rate']}")
        else:
            print(f"❌ 流程失败: {result.get('error', '未知错误')}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"💥 程序执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
