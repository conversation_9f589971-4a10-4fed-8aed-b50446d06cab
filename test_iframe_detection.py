#!/usr/bin/env python3
"""
测试iframe验证码检测功能
"""

import asyncio
import sys
import os
import yaml

# 添加scripts目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

from playwright_yop import PlaywrightYopManager

def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'input', 'config.yaml')
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

async def test_iframe_detection():
    """测试iframe验证码检测功能"""
    
    # 加载配置
    config = load_config()
    
    # 配置PlaywrightYopManager
    manager_config = {
        'capsolver': config.get('capsolver', {}),
        'playwright_yop': config.get('playwright_yop', {})
    }
    
    # 初始化PlaywrightYopManager
    manager = PlaywrightYopManager(config=manager_config, enable_debug=True)
    
    try:
        print("🚀 启动浏览器...")
        await manager.start_browser()
        
        print("📧 导航到yopmail (elizabeth)...")
        page = await manager.navigate_to_inbox("elizabeth")
        
        print("🔍 测试iframe验证码检测功能...")
        print("=" * 60)
        
        # 直接测试iframe检测方法
        iframe_detected = await manager._detect_iframe_captcha(page)
        
        print("=" * 60)
        if iframe_detected:
            print("✅ 成功检测到iframe中的验证码！")
            print("🎉 iframe检测功能工作正常")
        else:
            print("ℹ️ 未检测到iframe验证码")
            
        # 等待一段时间以便观察结果
        print("⏳ 等待5秒以观察结果...")
        await asyncio.sleep(5)
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("🔚 关闭浏览器...")
        await manager.close_browser()

if __name__ == "__main__":
    print("=== 测试iframe验证码检测功能 ===")
    print("🔍 测试新的iframe检测逻辑")
    print()
    asyncio.run(test_iframe_detection())
