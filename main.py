"""
主程序入口 - IX浏览器自动化注册Resend账户
实现完整的注册流程：IX浏览器创建 → Playwright连接 → 域名获取 → 注册 → 验证
"""

import asyncio
import time
import os
import sys
import logging
import random
import yaml

# 添加项目根目录到路径
sys.path.append('.')

from scripts.browser_manager import BrowserManager
from scripts.playwright_registration import PlaywrightRegistration
from scripts.yogo_python_client import YogoPythonClient  # 保留域名获取功能
from scripts.playwright_yop import get_resend_verification_link, shutdown_global_manager  # 新的邮件获取功能
from scripts.resend_http_client import ResendHTTPManager


def load_registration_config():
    """
    加载注册配置

    Returns:
        dict: 注册配置
    """
    try:
        with open('input/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        registration_settings = config.get('registration_settings', {})

        return {
            'interval_min': registration_settings.get('interval_min', 30),
            'interval_max': registration_settings.get('interval_max', 120),
            'target_count': registration_settings.get('target_count', 1)
        }
    except Exception as e:
        print(f"加载注册配置失败: {e}")
        # 返回默认配置
        return {
            'interval_min': 30,
            'interval_max': 120,
            'target_count': 1
        }


async def wait_with_countdown(wait_seconds, logger):
    """
    等待指定时间，并显示倒计时

    Args:
        wait_seconds: 等待时间（秒）
        logger: 日志记录器
    """
    logger.info(f"⏳ 等待 {wait_seconds} 秒后开始下一个注册...")

    for remaining in range(wait_seconds, 0, -1):
        # 使用 \r 实现同一行更新
        print(f"\r⏳ 倒计时: {remaining:3d} 秒", end='', flush=True)
        await asyncio.sleep(1)

    # 清除倒计时行并输出完成信息
    print(f"\r✅ 等待完成，开始下一个注册" + " " * 20)
    logger.info("✅ 等待完成，开始下一个注册")


def setup_main_logging():
    """设置主程序的日志系统"""
    os.makedirs("output", exist_ok=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
    log_filename = f'output/main_logs_{timestamp}.txt'

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)


def save_account_to_file(email, password, api_key):
    """
    将账户信息保存到文件

    Args:
        email: 用户邮箱
        password: 用户密码
        api_key: API密钥
    """
    # 确保output文件夹存在
    os.makedirs("output", exist_ok=True)

    # 文件路径
    file_path = "output/registered_accounts.txt"

    # 格式化内容：[api密钥]---[用户邮箱 密码]
    content = f"[{api_key}]---[{email} {password}]"

    try:
        # 检查文件是否存在以及是否需要添加换行符
        file_exists = os.path.exists(file_path)
        needs_newline = False
        
        if file_exists:
            # 检查文件最后一个字符是否为换行符
            with open(file_path, 'rb') as f:
                f.seek(0, 2)  # 移动到文件末尾
                file_size = f.tell()
                if file_size > 0:
                    f.seek(-1, 2)  # 移动到文件末尾前一个字符
                    last_char = f.read(1)
                    needs_newline = last_char != b'\n'
                else:
                    needs_newline = False  # 空文件不需要换行符
        
        # 追加模式写入文件
        with open(file_path, 'a', encoding='utf-8') as f:
            if file_exists and needs_newline:
                f.write('\n')  # 先添加换行符
            f.write(content + '\n')  # 写入内容并添加换行符

        logger = logging.getLogger(__name__)
        logger.info(f"✅ 账户信息已保存到文件: {file_path}")
        logger.info(f"   内容: [{api_key}]---[{email} {password}]")

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"❌ 保存账户信息到文件失败: {e}")
        raise


def extract_main_domain(domain):
    """
    从子域名中提取主域名
    例如: hr.maxupp.com -> maxupp.com
    
    Args:
        domain: 完整域名
        
    Returns:
        str: 主域名
    """
    parts = domain.split('.')
    if len(parts) >= 2:
        return '.'.join(parts[-2:])  # 取最后两部分作为主域名
    return domain


def generate_smtp_output_file(successful_results):
    """
    生成SMTP配置格式的输出文件
    按主域名分组，为每个主域名生成单独的文件
    
    Args:
        successful_results: 成功的域名添加结果列表
    """
    logger = logging.getLogger(__name__)
    
    if not successful_results:
        logger.warning("⚠️ 没有成功的结果，跳过SMTP文件生成")
        return
    
    try:
        # 按主域名分组
        domain_groups = {}
        for result in successful_results:
            main_domain = extract_main_domain(result.domain.domain)
            if main_domain not in domain_groups:
                domain_groups[main_domain] = []
            domain_groups[main_domain].append(result)
        
        logger.info(f"📄 按主域名分组生成SMTP配置文件，共 {len(domain_groups)} 个主域名")
        
        # 确保output文件夹存在
        os.makedirs("output", exist_ok=True)
        
        date_str = time.strftime("%Y%m%d", time.localtime())
        generated_files = []
        
        # 为每个主域名生成单独的文件
        for main_domain, results in domain_groups.items():
            count = len(results)
            # 文件名格式：主域名-日期-该主域名数量.txt
            filename = f"{main_domain}-{date_str}-{count}.txt"
            file_path = f"output/{filename}"
            
            logger.info(f"📋 生成主域名 {main_domain} 的SMTP配置文件: {filename}")
            
            # 写入文件内容
            with open(file_path, 'w', encoding='utf-8') as f:
                # 添加文件头部信息
                f.write(f"# SMTP配置文件 - 主域名: {main_domain}\n")
                f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 子域名数量: {count}\n")
                f.write(f"# 格式: [域名]---[SMTP服务器]:[端口]---[协议]---[API密钥]---[用户邮箱 密码]\n\n")
                
                for result in results:
                    # 适配resend_http_client.py的TaskResult数据结构
                    domain = result.domain.domain
                    api_key = result.account.api_key
                    email = result.account.email
                    password = result.account.password
                    
                    # 格式: [域名]---[smtp.resend.com]:[465]---[resend]---[API密钥]---[用户邮箱 密码]
                    line = f"[{domain}]---[smtp.resend.com]:[465]---[resend]---[{api_key}]---[{email} {password}]\n"
                    f.write(line)
            
            generated_files.append(filename)
            logger.info(f"✅ 主域名 {main_domain} 的SMTP配置文件已生成: {file_path}")
            logger.info(f"   包含子域名数量: {count}")
            
            # 显示该主域名的文件内容预览
            logger.info(f"📋 {main_domain} 文件内容预览:")
            for i, result in enumerate(results[:2], 1):  # 每个主域名显示前2条
                domain = result.domain.domain
                api_key = result.account.api_key[:10] + "..."
                email = result.account.email
                logger.info(f"   {i}. [{domain}]---[smtp.resend.com]:[465]---[resend]---[{api_key}]---[{email} ...]")
            
            if len(results) > 2:
                logger.info(f"   ... 还有 {len(results) - 2} 条记录")
            logger.info("")  # 空行分隔
        
        # 总结信息
        total_domains = sum(len(results) for results in domain_groups.values())
        logger.info("=" * 60)
        logger.info("📊 SMTP配置文件生成完成总结:")
        logger.info(f"   生成文件数量: {len(generated_files)} 个")
        logger.info(f"   主域名数量: {len(domain_groups)} 个")
        logger.info(f"   总子域名数量: {total_domains} 个")
        logger.info("   生成的文件:")
        for filename in generated_files:
            logger.info(f"     - {filename}")
        logger.info("=" * 60)
            
    except Exception as e:
        logger.error(f"❌ 生成SMTP配置文件失败: {e}")
        raise


async def run_domain_addition():
    """
    运行域名添加流程
    
    Returns:
        list: 成功的域名添加结果
    """
    logger = logging.getLogger(__name__)
    logger.info("🌐 开始域名添加流程")
    logger.info("=" * 60)
    
    try:
        # 创建ResendHTTPManager实例
        resend_manager = ResendHTTPManager()
        
        # 运行域名添加
        result = resend_manager.run_domain_addition()
        
        if result["success"]:
            successful_results = result["successful_results"]
            stats = result["statistics"]
            
            logger.info("✅ 域名添加流程完成！")
            logger.info(f"📊 统计结果:")
            logger.info(f"   总任务: {stats['total_tasks']}")
            logger.info(f"   成功: {stats['successful_tasks']}")
            logger.info(f"   失败: {stats['failed_tasks']}")
            logger.info(f"   成功率: {stats['success_rate']}")
            
            return successful_results
        else:
            logger.error(f"❌ 域名添加流程失败: {result.get('error', '未知错误')}")
            return []
            
    except Exception as e:
        logger.error(f"💥 域名添加流程异常: {e}")
        import traceback
        traceback.print_exc()
        return []


async def run_cf_dns_resolution():
    """
    运行Cloudflare DNS解析流程
    
    Returns:
        bool: 解析是否成功
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 导入CF DNS解析器
        from scripts.cf_dns_resolver import CloudflareDNSResolver
        
        # 创建解析器实例
        resolver = CloudflareDNSResolver()
        
        # 运行DNS解析（已去除确认提示，自动执行）
        resolver.run_dns_resolution()
        
        logger.info("✅ Cloudflare DNS解析流程完成")
        return True
        
    except Exception as e:
        logger.error(f"💥 Cloudflare DNS解析流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_resend_verification():
    """
    运行Resend域名验证流程
    
    Returns:
        bool: 验证是否成功
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 导入Resend域名管理器
        from scripts.resend_http_client import ResendHTTPManager

        # 创建管理器实例
        manager = ResendHTTPManager()
        
        # 加载账户文件
        if not manager.load_accounts_from_file("output/registered_accounts.txt"):
            logger.error("❌ 无法加载账户文件，验证失败")
            return False
        
        logger.info(f"✅ 已加载 {len(manager.accounts)} 个账户")
        
        # 收集所有域名
        logger.info("🔍 正在查询所有账户的域名列表...")
        all_domains = []  # [(account, domain_info)]
        
        for i, account in enumerate(manager.accounts, 1):
            logger.info(f"📋 查询账户 {i}/{len(manager.accounts)}: {account.email}")
            
            success, result, error_message, status_code = manager.http_client.list_domains(account.api_key)
            
            if success and result:
                domains = result.get('data', [])
                for domain in domains:
                    all_domains.append((account, domain))
                logger.info(f"   找到 {len(domains)} 个域名")
            else:
                logger.warning(f"   ⚠️ 查询失败: {error_message}")
            
            # API限制
            if i < len(manager.accounts):
                manager.wait_for_rate_limit()
        
        if not all_domains:
            logger.warning("📭 所有账户都没有域名")
            return False
        
        # 去重 - 使用域名ID作为唯一标识
        seen_domain_ids = set()
        unique_domains = []
        for account, domain in all_domains:
            domain_id = domain.get('id')
            if domain_id not in seen_domain_ids:
                seen_domain_ids.add(domain_id)
                unique_domains.append((account, domain))
        
        selected_domains = unique_domains
        
        logger.info(f"📋 找到 {len(selected_domains)} 个唯一域名，开始自动验证")
        
        logger.info("🔍 开始验证域名...")
        
        # 验证所有域名
        success_count = 0
        for i, (account, domain) in enumerate(selected_domains, 1):
            domain_id = domain.get('id')
            domain_name = domain.get('name')
            
            logger.info(f"📋 验证域名 {i}/{len(selected_domains)}: {domain_name}")
            logger.info(f"   账户: {account.email}")
            
            success, result, error_message, status_code = manager.http_client.verify_domain(
                api_key=account.api_key,
                domain_id=domain_id
            )
            
            if success and result:
                logger.info("✅ 验证请求已发送！")
                success_count += 1
            else:
                logger.warning(f"❌ 验证失败: {error_message}")
            
            # API限制
            if i < len(selected_domains):
                manager.wait_for_rate_limit()
        
        logger.info(f"📊 验证完成: 成功 {success_count}/{len(selected_domains)} 个")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"💥 Resend域名验证流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def register_single_resend_account():
    """
    注册单个Resend账户的完整流程
    
    按照以下步骤执行：
    1. 创建IX浏览器
    2. Playwright连接IX浏览器
    3. 获取yopmail备用域名
    4. 选择可用域名（过滤黑名单）
    5. 生成邮箱和密码
    6. 尝试注册（失败则换域名重试）
    7. 记录账户信息
    8. 获取验证邮件
    9. 完成邮箱验证
    
    Returns:
        dict: 注册结果
    """
    logger = logging.getLogger(__name__)
    logger.info("🚀 开始注册单个Resend账户")

    # 初始化管理器
    browser_mgr = BrowserManager()
    playwright_mgr = PlaywrightRegistration(browser_mgr)
    # 域名获取阶段：使用简化的域名获取客户端
    yopmail_client = YogoPythonClient(enable_debug=False)
    
    profile_id = None
    registration_result = {
        "success": False,
        "email": None,
        "password": None,
        "api_key": None,
        "profile_id": None,
        "verification_completed": False,
        "error": None
    }
    
    try:
        # 步骤1: 创建IX浏览器
        logger.info("📋 步骤1: 创建IX浏览器")
        profile_result = browser_mgr.create_profile()
        if not profile_result:
            raise Exception("创建IX浏览器配置文件失败")
        
        profile_id = profile_result.get('profile_id')
        if not profile_id:
            raise Exception("创建IX浏览器配置文件失败：未获取到Profile ID")

        registration_result["profile_id"] = profile_id
        logger.info(f"✅ 成功创建IX浏览器，Profile ID: {profile_id}")
        
        # 步骤2: 打开IX浏览器并连接Playwright（带重试机制）
        logger.info("🔗 步骤2: 打开IX浏览器并连接Playwright")
        browser_info = browser_mgr.open_browser_with_retry(
            profile_id,
            max_retries=3,    # 最多重试3次
            retry_delay=2     # 每次间隔2秒
        )
        if not browser_info:
            raise Exception("打开IX浏览器失败，已重试3次")
        
        logger.info("✅ IX浏览器打开成功")
        
        # 等待浏览器启动
        await asyncio.sleep(3)
        
        # Playwright连接IX浏览器
        connection_success = await playwright_mgr.connect_to_ix_browser(browser_info, profile_id=profile_id)
        if not connection_success:
            raise Exception("Playwright连接IX浏览器失败")
        
        # Playwright连接成功（日志已简化）

        # 步骤3: 获取yopmail备用域名
        domains = yopmail_client.get_yopmail_alternative_domains()
        if not domains:
            raise Exception("获取yopmail备用域名失败")

        logger.info(f"✅ 获取到 {len(domains)} 个可用域名")

        # 步骤4-6: 域名选择和注册循环
        max_domain_attempts = 5  # 最多尝试5个域名
        selected_domain = None  # 初始化变量

        for attempt in range(max_domain_attempts):
            try:
                # 步骤4: 获取随机域名
                selected_domain = yopmail_client.get_random_yopmail_domain()
                if not selected_domain:
                    raise Exception("没有可用的域名")

                logger.info(f"🎲 选择域名: {selected_domain} (尝试 {attempt + 1}/{max_domain_attempts})")

                # 步骤5: 生成邮箱和密码
                email = playwright_mgr.generate_yopmail_email(selected_domain)
                password = playwright_mgr.generate_strong_password()

                logger.info(f"📧 生成邮箱: {email}")
                logger.info(f"🔑 生成密码: {password}")

                # 步骤6: 注册和邮件获取循环
                max_registration_attempts = 3  # 每个域名最多尝试3次完整的注册+邮件获取流程

                for reg_attempt in range(max_registration_attempts):
                    logger.info(f"📝 开始注册流程 (域名 {selected_domain} 第 {reg_attempt + 1}/{max_registration_attempts} 次尝试)")

                    # 重新生成邮箱和密码（每次尝试都用新的）
                    if reg_attempt > 0:
                        email = playwright_mgr.generate_yopmail_email(selected_domain)
                        password = playwright_mgr.generate_strong_password()
                        logger.info(f"📧 重新生成邮箱: {email}")
                        logger.info(f"🔑 重新生成密码: {password}")

                    # 尝试注册
                    register_result = await playwright_mgr.register_resend_account(email, password)

                    if register_result["success"]:
                        logger.info("✅ 注册成功！正在获取验证邮件")

                        # 步骤7: 获取验证邮件（yopmail模块内部处理重试，最多5次）
                        email_prefix = email.split('@')[0]

                        verification_link = await get_resend_verification_link(
                            email_prefix,
                            max_attempts=5,   # 最多尝试5次
                            wait_interval=5   # 每次等待5秒
                        )

                        if verification_link:
                            logger.info("✅ 获取到验证链接，正在验证邮箱")

                            # 步骤8: 完成邮箱验证
                            verification_result = await playwright_mgr.verify_email(verification_link, email)
                            if verification_result["success"]:
                                logger.info("🎉 邮箱验证和API Key获取成功！")

                                # 记录成功的账户信息
                                registration_result["email"] = email
                                registration_result["password"] = password
                                registration_result["api_key"] = verification_result.get("api_key")
                                registration_result["verification_completed"] = True
                                registration_result["success"] = True

                                # 跳出所有循环
                                attempt = max_domain_attempts
                                break
                            else:
                                logger.warning(f"⚠️ 邮箱验证失败: {verification_result.get('message', '未知错误')}，重新尝试注册")
                                continue
                        else:
                            logger.warning("⚠️ 5次都未获取到验证邮件，重新尝试注册")
                            logger.info(f"🔄 准备重新注册，当前尝试次数: {reg_attempt + 1}/{max_registration_attempts}")
                            continue

                    elif register_result.get("error") == "DISPOSABLE_EMAIL":
                        logger.warning(f"❌ 域名 {selected_domain} 被Resend拒绝，换下一个域名")
                        break  # 跳出注册尝试循环，换域名
                    else:
                        logger.warning(f"⚠️ 注册失败: {register_result.get('message', '未知错误')} - 重新尝试")
                        continue  # 重新尝试注册
                else:
                    # 3次注册尝试都失败，换域名
                    logger.error(f"❌ 域名 {selected_domain} 尝试 {max_registration_attempts} 次都失败，换下一个域名")
                    continue

                # 如果成功，跳出外层循环
                if registration_result.get("success"):
                    break

            except Exception as e:
                domain_info = f" {selected_domain}" if selected_domain else ""
                logger.error(f"❌ 域名{domain_info} 注册异常: {e} - 换下一个域名")
                continue  # 换域名
        else:
            raise Exception(f"尝试了 {max_domain_attempts} 个域名都失败")

        # 步骤7: 更新IX浏览器配置（只有完全成功后才执行）
        if registration_result.get("success"):
            logger.info("📊 步骤7: 记录账户信息并更新IX浏览器配置")
            final_email = registration_result["email"]
            final_password = registration_result["password"]

            logger.info(f"✅ 最终成功的账户:")
            logger.info(f"   邮箱: {final_email}")
            logger.info(f"   密码: {final_password}")
            logger.info(f"   API Key: {registration_result.get('api_key', 'N/A')}")
            logger.info(f"   Profile ID: {profile_id}")

            # 直接传递数据更新IX浏览器配置文件的4个字段
            logger.info("🔄 更新IX浏览器配置文件...")
            final_api_key = registration_result.get('api_key', 'N/A')
            browser_mgr.update_profile(
                profile_id=profile_id,
                name=final_email,                           # 窗口名称设为邮箱
                username=final_email,                       # 平台用户名设为邮箱
                password=final_password,                    # 平台密码
                api_key=final_api_key                       # API密钥（IX模块会自动格式化备注）
            )
            logger.info("✅ IX浏览器配置文件更新完成")

            # 步骤8: 保存账户信息到文件
            logger.info("📄 步骤8: 保存账户信息到文件")
            try:
                save_account_to_file(final_email, final_password, final_api_key)
                logger.info("✅ 账户信息文件保存完成")
            except Exception as e:
                logger.error(f"❌ 保存账户信息到文件失败: {e}")
                # 不抛出异常，因为注册已经成功，文件保存失败不应该影响整体结果
        
        return registration_result
        
    except Exception as e:
        logger.error(f"💥 注册流程失败: {e}")
        registration_result["error"] = str(e)
        return registration_result
        
    finally:
        # 清理资源
        logger.info("🧹 清理资源")
        
        # 关闭Playwright
        if playwright_mgr:
            try:
                await playwright_mgr.close_browser()
                logger.info("✅ Playwright已关闭")
            except Exception as e:
                logger.warning(f"⚠️ 关闭Playwright时出错: {e}")
        
        # 关闭IX浏览器
        if profile_id and browser_mgr:
            try:
                close_result = browser_mgr.close_browser(profile_id)
                if close_result:
                    logger.info("✅ IX浏览器已关闭")
                else:
                    logger.warning("⚠️ 关闭IX浏览器失败")
            except Exception as e:
                logger.warning(f"⚠️ 关闭IX浏览器时出错: {e}")


async def main():
    """主函数 - 批量注册模式"""
    logger = setup_main_logging()

    # 加载注册配置
    config = load_registration_config()

    logger.info("🎯 IX浏览器自动化注册Resend账户 - 批量模式")
    logger.info("=" * 60)
    logger.info(f"📊 注册配置:")
    logger.info(f"   目标数量: {config['target_count']} 个成功注册")
    logger.info(f"   间隔时间: {config['interval_min']}-{config['interval_max']} 秒")
    logger.info("=" * 60)

    # 统计变量
    success_count = 0
    failed_count = 0
    total_attempts = 0
    start_time = time.time()

    try:
        # 修正：循环直到达到目标成功数量，而不是固定次数
        while success_count < config['target_count']:
            total_attempts += 1
            logger.info(f"\n🚀 开始第 {total_attempts} 次尝试 (目标: {success_count + 1}/{config['target_count']} 个成功注册)")
            logger.info("-" * 40)

            # 注册单个账户
            result = await register_single_resend_account()

            # 统计结果
            if result["success"]:
                success_count += 1
                logger.info(f"✅ 第 {success_count} 个账户注册成功！")
                logger.info(f"   邮箱: {result['email']}")
                logger.info(f"   密码: {result['password']}")
                logger.info(f"   API Key: {result.get('api_key', 'N/A')}")
                logger.info(f"   Profile ID: {result['profile_id']}")
            else:
                failed_count += 1
                logger.error(f"❌ 第 {total_attempts} 次尝试失败！")
                logger.error(f"   错误信息: {result['error']}")

            # 显示当前进度
            logger.info(f"📈 当前进度: 成功 {success_count}/{config['target_count']} 个，失败 {failed_count} 个，总尝试 {total_attempts} 次")

            # 如果还没达到目标数量，则等待随机间隔
            if success_count < config['target_count']:
                # 生成随机等待时间
                wait_seconds = random.randint(config['interval_min'], config['interval_max'])
                logger.info(f"🎲 随机选择等待时间: {wait_seconds} 秒")

                # 等待并显示倒计时
                await wait_with_countdown(wait_seconds, logger)

        # 注册阶段统计
        registration_time = time.time() - start_time
        logger.info("\n" + "=" * 60)
        logger.info("📊 批量注册完成！注册阶段统计:")
        logger.info(f"   目标数量: {config['target_count']} 个")
        logger.info(f"   成功注册: {success_count} 个")
        logger.info(f"   失败注册: {failed_count} 个")
        logger.info(f"   总尝试次数: {total_attempts} 次")
        logger.info(f"   成功率: {success_count/total_attempts*100:.1f}%")
        logger.info(f"   注册耗时: {registration_time/60:.1f} 分钟")
        if success_count > 0:
            logger.info(f"   平均每个成功: {registration_time/success_count/60:.1f} 分钟")
        logger.info("=" * 60)

        # 如果有成功注册的账户，自动进行域名添加
        if success_count > 0:
            logger.info("\n🚀 开始自动域名添加流程...")
            logger.info("=" * 60)
            
            # 运行域名添加
            domain_start_time = time.time()
            successful_domain_results = await run_domain_addition()
            domain_time = time.time() - domain_start_time
            
            logger.info(f"⏱️ 域名添加耗时: {domain_time/60:.1f} 分钟")
            
            # 如果域名添加成功，继续CF DNS解析和域名验证
            if successful_domain_results:
                # CF DNS解析
                logger.info("\n🌐 开始Cloudflare DNS解析流程...")
                logger.info("=" * 60)
                
                cf_start_time = time.time()
                cf_success = await run_cf_dns_resolution()
                cf_time = time.time() - cf_start_time
                
                logger.info(f"⏱️ CF DNS解析耗时: {cf_time/60:.1f} 分钟")
                
                if cf_success:
                    # RE域名验证
                    logger.info("\n✅ 开始Resend域名验证流程...")
                    logger.info("=" * 60)
                    
                    verify_start_time = time.time()
                    verify_success = await run_resend_verification()
                    verify_time = time.time() - verify_start_time
                    
                    logger.info(f"⏱️ 域名验证耗时: {verify_time/60:.1f} 分钟")
                    
                    # 生成SMTP配置文件
                    logger.info("\n📄 生成SMTP配置文件...")
                    logger.info("-" * 40)
                    generate_smtp_output_file(successful_domain_results)
                    
                    # 最终统计
                    total_time = time.time() - start_time
                    logger.info("\n" + "=" * 60)
                    logger.info("🎉 全自动化流程完成！最终统计:")
                    logger.info(f"   注册成功: {success_count} 个账户")
                    logger.info(f"   域名添加: {len(successful_domain_results)} 个域名")
                    logger.info(f"   总耗时: {total_time/60:.1f} 分钟")
                    logger.info(f"   注册阶段: {registration_time/60:.1f} 分钟")
                    logger.info(f"   域名阶段: {domain_time/60:.1f} 分钟")
                    logger.info(f"   DNS解析阶段: {cf_time/60:.1f} 分钟")
                    logger.info(f"   域名验证阶段: {verify_time/60:.1f} 分钟")
                    logger.info("=" * 60)
                else:
                    logger.error("❌ CF DNS解析失败，跳过域名验证")
            else:
                logger.error("❌ 域名添加失败，跳过后续流程")
        else:
            logger.warning("⚠️ 没有成功注册的账户，跳过域名添加流程")

    except KeyboardInterrupt:
        logger.info("\n⚠️ 用户中断程序")
        logger.info(f"📊 中断时统计: 成功 {success_count} 个，失败 {failed_count} 个")
    except Exception as e:
        logger.error(f"💥 程序执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
